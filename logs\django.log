INFO 2025-05-25 11:57:56,128 autoreload 11188 15052 Watching for file changes with StatReloader
INFO 2025-05-25 11:58:19,065 autoreload 11188 15052 C:\Users\<USER>\devops\sbo_system\sbo_system\settings.py changed, reloading.
INFO 2025-05-25 11:58:20,047 autoreload 4964 15200 Watching for file changes with StatReloader
INFO 2025-05-25 11:58:26,225 autoreload 6904 15020 Watching for file changes with StatReloader
ERROR 2025-05-25 11:58:27,959 log 6904 10848 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: ordinances_category.description

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\devops\sbo_system\ordinances\views.py", line 49, in home
    return render(request, 'ordinances/home.html', context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\devops\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: ordinances_category.description
ERROR 2025-05-25 11:58:27,963 basehttp 6904 10848 "GET / HTTP/1.1" 500 228893
INFO 2025-05-25 12:00:52,177 autoreload 9280 6456 Watching for file changes with StatReloader
INFO 2025-05-25 12:01:00,335 basehttp 9280 5408 "GET / HTTP/1.1" 200 38966
INFO 2025-05-25 12:01:00,367 basehttp 9280 5408 "GET /static/css/custom.css HTTP/1.1" 304 0
INFO 2025-05-25 12:01:00,367 basehttp 9280 13176 "GET /static/img/dumingag-logo.png HTTP/1.1" 304 0
INFO 2025-05-25 12:03:47,482 autoreload 15272 8160 Watching for file changes with StatReloader
INFO 2025-05-25 12:04:11,843 autoreload 4516 8000 Watching for file changes with StatReloader
INFO 2025-05-25 12:04:13,752 basehttp 4516 10964 "GET / HTTP/1.1" 200 40311
